import {
    Descriptions,
    Spin,
    Tag,
    Timeline,
    Typography,
    Image,
    Alert,
    Divider,
    Row,
    Col,
    Statistic,
} from 'antd';
import { ClockCircleOutlined, UserOutlined, ShopOutlined } from '@ant-design/icons';
import moment from 'moment';

import { AppealStatus, AppealType, AppealStatusEnum } from '@/constants/AppealManagement';
import AppealActions from '../../List/components/AppealActions';

const { Title, Text, Paragraph } = Typography;

interface AppealDetailContentProps {
    appealDetail?: API.AppealDetail;
    loading: boolean;
    onRefresh: () => void;
}

const AppealDetailContent: React.FC<AppealDetailContentProps> = ({
    appealDetail,
    loading,
    onRefresh,
}) => {
    // 获取状态标签颜色
    const getStatusTagColor = (status: string) => {
        switch (status) {
            case AppealStatusEnum.PENDING:
                return 'orange';
            case AppealStatusEnum.PROCESSING:
                return 'blue';
            case AppealStatusEnum.PROCESSED:
                return 'green';
            case AppealStatusEnum.REJECTED:
                return 'red';
            case AppealStatusEnum.TIMEOUT:
                return 'volcano';
            case AppealStatusEnum.CLOSED:
                return 'default';
            case AppealStatusEnum.SECOND_APPEAL:
                return 'purple';
            default:
                return 'default';
        }
    };

    // 获取优先级标签
    const getPriorityTag = (priority: string) => {
        const priorityMap: Record<string, { text: string; color: string }> = {
            '01': { text: '低', color: 'default' },
            '02': { text: '中', color: 'orange' },
            '03': { text: '高', color: 'red' },
            '04': { text: '紧急', color: 'volcano' },
        };
        return priorityMap[priority] || { text: '未知', color: 'default' };
    };

    if (loading) {
        return <Spin spinning={true} style={{ minHeight: '400px' }} />;
    }

    // if (!appealDetail) {
    //     return (
    //         <div style={{ textAlign: 'center', padding: '50px' }}>
    //             <Text type="secondary">申诉详情不存在</Text>
    //         </div>
    //     );
    // }

    return (
        <Row gutter={16}>
            {/* 左侧主要信息 */}
            <Col span={16}>
                {/* 申诉概览 */}
                <div
                    style={{
                        marginBottom: '16px',
                        padding: '16px',
                        backgroundColor: '#fafafa',
                        borderRadius: '6px',
                    }}
                >
                    <Title level={4} style={{ marginBottom: '16px' }}>
                        申诉概览
                    </Title>
                    <Row gutter={16}>
                        <Col span={6}>
                            <Statistic
                                title="申诉单号"
                                value={appealDetail.appealNo}
                                valueStyle={{ fontSize: '16px', fontWeight: 'bold' }}
                            />
                        </Col>
                        <Col span={6}>
                            <div>
                                <div
                                    style={{ fontSize: '14px', color: '#666', marginBottom: '4px' }}
                                >
                                    申诉状态
                                </div>
                                <Tag color={getStatusTagColor(appealDetail.appealStatus)}>
                                    {
                                        AppealStatus[
                                            appealDetail.appealStatus as keyof typeof AppealStatus
                                        ]
                                    }
                                </Tag>
                            </div>
                        </Col>
                        <Col span={6}>
                            <Statistic
                                title="申诉金额"
                                value={appealDetail.appealAmount || 0}
                                precision={2}
                                prefix="¥"
                                valueStyle={{ color: '#cf1322' }}
                            />
                        </Col>
                        <Col span={6}>
                            <div>
                                <div
                                    style={{ fontSize: '14px', color: '#666', marginBottom: '4px' }}
                                >
                                    优先级
                                </div>
                                <Tag color={getPriorityTag(appealDetail.priority || '01').color}>
                                    {getPriorityTag(appealDetail.priority || '01').text}
                                </Tag>
                            </div>
                        </Col>
                    </Row>

                    {/* 时间信息 */}
                    <Divider />
                    <Row gutter={16}>
                        <Col span={8}>
                            <div style={{ textAlign: 'center' }}>
                                <ClockCircleOutlined
                                    style={{ fontSize: '16px', color: '#1890ff' }}
                                />
                                <div style={{ marginTop: '4px' }}>
                                    <Text type="secondary">申诉时间</Text>
                                    <br />
                                    <Text strong>
                                        {moment(appealDetail.appealTime).format(
                                            'YYYY-MM-DD HH:mm:ss',
                                        )}
                                    </Text>
                                </div>
                            </div>
                        </Col>
                        <Col span={8}>
                            <div style={{ textAlign: 'center' }}>
                                <ClockCircleOutlined
                                    style={{ fontSize: '16px', color: '#faad14' }}
                                />
                                <div style={{ marginTop: '4px' }}>
                                    <Text type="secondary">处理截止时间</Text>
                                    <br />
                                    <Text strong>
                                        {appealDetail.deadlineTime
                                            ? moment(appealDetail.deadlineTime).format(
                                                  'YYYY-MM-DD HH:mm:ss',
                                              )
                                            : '无限制'}
                                    </Text>
                                </div>
                            </div>
                        </Col>
                        <Col span={8}>
                            <div style={{ textAlign: 'center' }}>
                                <ClockCircleOutlined
                                    style={{ fontSize: '16px', color: '#52c41a' }}
                                />
                                <div style={{ marginTop: '4px' }}>
                                    <Text type="secondary">处理时间</Text>
                                    <br />
                                    <Text strong>
                                        {appealDetail.handleTime
                                            ? moment(appealDetail.handleTime).format(
                                                  'YYYY-MM-DD HH:mm:ss',
                                              )
                                            : '未处理'}
                                    </Text>
                                </div>
                            </div>
                        </Col>
                    </Row>
                </div>

                {/* 基本信息 */}
                <div
                    style={{
                        marginBottom: '16px',
                        padding: '16px',
                        backgroundColor: '#fff',
                        border: '1px solid #d9d9d9',
                        borderRadius: '6px',
                    }}
                >
                    <Title level={4} style={{ marginBottom: '16px' }}>
                        基本信息
                    </Title>
                    <Descriptions column={2} bordered>
                        <Descriptions.Item
                            label={
                                <>
                                    <UserOutlined /> 用户手机号
                                </>
                            }
                        >
                            {appealDetail.userPhone}
                        </Descriptions.Item>
                        <Descriptions.Item
                            label={
                                <>
                                    <ShopOutlined /> 商户号
                                </>
                            }
                        >
                            {appealDetail.merchantId}
                        </Descriptions.Item>
                        <Descriptions.Item label="申诉类型">
                            <Tag color="blue">
                                {AppealType[appealDetail.appealType as keyof typeof AppealType]}
                            </Tag>
                        </Descriptions.Item>
                        <Descriptions.Item label="关联订单">
                            {appealDetail.orderNo || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label="站点名称">
                            {appealDetail.stationName || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label="运营商">
                            {appealDetail.operatorName || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label="城市">
                            {appealDetail.cityName || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label="处理人">
                            {appealDetail.handlePerson || '-'}
                        </Descriptions.Item>
                    </Descriptions>
                </div>

                {/* 申诉内容 */}
                <div
                    style={{
                        marginBottom: '16px',
                        padding: '16px',
                        backgroundColor: '#fff',
                        border: '1px solid #d9d9d9',
                        borderRadius: '6px',
                    }}
                >
                    <Title level={4} style={{ marginBottom: '16px' }}>
                        申诉内容
                    </Title>
                    <div style={{ marginBottom: '16px' }}>
                        <Title level={5} style={{ marginBottom: '8px' }}>
                            申诉标题
                        </Title>
                        <Paragraph style={{ fontSize: '16px', marginBottom: '16px' }}>
                            {appealDetail.appealTitle}
                        </Paragraph>
                    </div>
                    <div style={{ marginBottom: '16px' }}>
                        <Title level={5} style={{ marginBottom: '8px' }}>
                            申诉详情
                        </Title>
                        <Paragraph
                            style={{
                                backgroundColor: '#fafafa',
                                padding: '12px',
                                borderRadius: '6px',
                                border: '1px solid #d9d9d9',
                            }}
                        >
                            {appealDetail.appealContent}
                        </Paragraph>
                    </div>
                    {appealDetail.attachments && appealDetail.attachments.length > 0 && (
                        <div>
                            <Title level={5} style={{ marginBottom: '12px' }}>
                                相关附件
                            </Title>
                            <div style={{ display: 'flex', gap: '12px', flexWrap: 'wrap' }}>
                                {appealDetail.attachments.map((attachment, index) => (
                                    <div key={index} style={{ textAlign: 'center' }}>
                                        <Image
                                            width={120}
                                            height={120}
                                            src={attachment.fileUrl}
                                            alt={attachment.fileName}
                                            style={{
                                                objectFit: 'cover',
                                                borderRadius: '6px',
                                                border: '1px solid #d9d9d9',
                                            }}
                                        />
                                        <div
                                            style={{
                                                marginTop: '4px',
                                                fontSize: '12px',
                                                color: '#666',
                                                maxWidth: '120px',
                                                overflow: 'hidden',
                                                textOverflow: 'ellipsis',
                                                whiteSpace: 'nowrap',
                                            }}
                                        >
                                            {attachment.fileName}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}
                </div>

                {/* 处理结果 */}
                {appealDetail.handleResult && (
                    <div
                        style={{
                            marginBottom: '16px',
                            padding: '16px',
                            backgroundColor: '#fff',
                            border: '1px solid #d9d9d9',
                            borderRadius: '6px',
                        }}
                    >
                        <Title level={4} style={{ marginBottom: '16px' }}>
                            处理结果
                        </Title>
                        <Alert
                            message="处理完成"
                            description={appealDetail.handleRemark}
                            type="success"
                            showIcon
                            style={{ marginBottom: '16px' }}
                        />
                        <Descriptions column={2} bordered>
                            <Descriptions.Item label="处理结果">
                                <Tag color="green">{appealDetail.handleResult}</Tag>
                            </Descriptions.Item>
                            <Descriptions.Item label="处理人">
                                {appealDetail.handlePerson || '-'}
                            </Descriptions.Item>
                            <Descriptions.Item label="处理时间" span={2}>
                                {appealDetail.handleTime
                                    ? moment(appealDetail.handleTime).format('YYYY-MM-DD HH:mm:ss')
                                    : '-'}
                            </Descriptions.Item>
                            <Descriptions.Item label="处理说明" span={2}>
                                {appealDetail.handleRemark || '-'}
                            </Descriptions.Item>
                        </Descriptions>
                    </div>
                )}
            </Col>

            {/* 右侧操作和指引 */}
            <Col span={8}>
                {/* 操作区域 */}
                <div
                    style={{
                        marginBottom: '16px',
                        padding: '16px',
                        backgroundColor: '#fff',
                        border: '1px solid #d9d9d9',
                        borderRadius: '6px',
                    }}
                >
                    <Title level={4} style={{ marginBottom: '16px' }}>
                        操作
                    </Title>
                    <AppealActions record={appealDetail} onSuccess={onRefresh} />
                </div>

                {/* 操作指引 */}
                <div
                    style={{
                        marginBottom: '16px',
                        padding: '16px',
                        backgroundColor: '#fff',
                        border: '1px solid #d9d9d9',
                        borderRadius: '6px',
                    }}
                >
                    <Title level={4} style={{ marginBottom: '16px' }}>
                        操作指引
                    </Title>
                    <div style={{ lineHeight: '1.8' }}>
                        <Title level={5} style={{ marginBottom: '12px' }}>
                            处理流程
                        </Title>
                        <div style={{ marginBottom: '16px' }}>
                            <Text type="secondary">1. 查看申诉详情和相关证据</Text>
                            <br />
                            <Text type="secondary">2. 核实申诉内容的真实性</Text>
                            <br />
                            <Text type="secondary">3. 根据情况选择处理方式</Text>
                            <br />
                            <Text type="secondary">4. 填写处理说明并提交</Text>
                        </div>

                        <Title level={5} style={{ marginBottom: '12px' }}>
                            注意事项
                        </Title>
                        <div style={{ marginBottom: '16px' }}>
                            <Text type="secondary">• 请在截止时间前完成处理</Text>
                            <br />
                            <Text type="secondary">• 处理说明需详细说明原因</Text>
                            <br />
                            <Text type="secondary">• 如需退款请填写准确金额</Text>
                            <br />
                            <Text type="secondary">• 处理结果将通知用户</Text>
                        </div>

                        <Title level={5} style={{ marginBottom: '12px' }}>
                            联系方式
                        </Title>
                        <div>
                            <Text type="secondary">客服热线：400-123-4567</Text>
                            <br />
                            <Text type="secondary">工作时间：9:00-18:00</Text>
                        </div>
                    </div>
                </div>

                {/* 处理时间线 */}
                {appealDetail.handleHistory && appealDetail.handleHistory.length > 0 && (
                    <div
                        style={{
                            padding: '16px',
                            backgroundColor: '#fff',
                            border: '1px solid #d9d9d9',
                            borderRadius: '6px',
                        }}
                    >
                        <Title level={4} style={{ marginBottom: '16px' }}>
                            处理记录
                        </Title>
                        <Timeline>
                            {appealDetail.handleHistory.map((record, index) => (
                                <Timeline.Item key={index} color={index === 0 ? 'green' : 'blue'}>
                                    <div>
                                        <Text strong style={{ color: '#1890ff' }}>
                                            {record.operationTypeName}
                                        </Text>
                                        <br />
                                        <Text type="secondary" style={{ fontSize: '12px' }}>
                                            {moment(record.operationTime).format('MM-DD HH:mm')}
                                        </Text>
                                        <br />
                                        <Text style={{ fontSize: '13px' }}>
                                            操作人：{record.operatorName}
                                        </Text>
                                        {record.operationContent && (
                                            <>
                                                <br />
                                                <Text
                                                    style={{
                                                        fontSize: '13px',
                                                        color: '#666',
                                                        fontStyle: 'italic',
                                                    }}
                                                >
                                                    {record.operationContent}
                                                </Text>
                                            </>
                                        )}
                                    </div>
                                </Timeline.Item>
                            ))}
                        </Timeline>
                    </div>
                )}
            </Col>
        </Row>
    );
};

export default AppealDetailContent;
