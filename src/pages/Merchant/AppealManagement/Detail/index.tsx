import ProCard from '@ant-design/pro-card';
import { LeftOutlined } from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { useRequest } from 'ahooks';
import { message } from 'antd';
import { useLocation, history } from 'umi';

import { getAppealDetailApi } from '@/services/Merchant/AppealManagementApi';
import AppealDetailContent from './components/AppealDetailContent';

const AppealDetailPage = () => {
    const location = useLocation();
    const query = new URLSearchParams(location.search);
    const appealNo = query.get('appealNo');

    const goBack = () => {
        history.goBack();
    };

    const {
        data: appealDetail,
        loading,
        refresh,
    } = useRequest(() => getAppealDetailApi(appealNo!), {
        ready: !!appealNo,
        onError: () => {
            message.error('获取申诉详情失败');
        },
    });

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    申诉详情
                </div>
            }
        >
            <ProCard>
                <AppealDetailContent
                    appealDetail={appealDetail?.data}
                    loading={loading}
                    onRefresh={refresh}
                />
            </ProCard>
        </PageHeaderWrapper>
    );
};

export default AppealDetailPage;
