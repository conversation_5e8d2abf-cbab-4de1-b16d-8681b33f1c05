import ProCard from '@ant-design/pro-card';
import { LeftOutlined, ClockCircleOutlined, UserOutlined, ShopOutlined } from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { useRequest } from 'ahooks';
import {
    Descriptions,
    Spin,
    Tag,
    Timeline,
    Typography,
    Image,
    message,
    Alert,
    Divider,
    Row,
    Col,
    Statistic,
} from 'antd';

import { useLocation, history } from 'umi';
import moment from 'moment';

import { getAppealDetailApi } from '@/services/Merchant/AppealManagementApi';
import { AppealStatus, AppealType, AppealStatusEnum } from '@/constants/AppealManagement';
import AppealActions from '../List/components/AppealActions';

const { Title, Text, Paragraph } = Typography;

const AppealDetailPage = () => {
    const location = useLocation();
    const query = new URLSearchParams(location.search);
    const appealNo = query.get('appealNo');

    const {
        data: appealDetail,
        loading,
        refresh,
    } = useRequest(() => getAppealDetailApi(appealNo!), {
        ready: !!appealNo,
        onError: () => {
            message.error('获取申诉详情失败');
        },
    });

    // 获取状态标签颜色
    const getStatusTagColor = (status: string) => {
        switch (status) {
            case AppealStatusEnum.PENDING:
                return 'orange';
            case AppealStatusEnum.PROCESSING:
                return 'blue';
            case AppealStatusEnum.PROCESSED:
                return 'green';
            case AppealStatusEnum.REJECTED:
                return 'red';
            case AppealStatusEnum.TIMEOUT:
                return 'volcano';
            case AppealStatusEnum.CLOSED:
                return 'default';
            case AppealStatusEnum.SECOND_APPEAL:
                return 'purple';
            default:
                return 'default';
        }
    };

    // 获取优先级标签
    const getPriorityTag = (priority: string) => {
        const priorityMap: Record<string, { text: string; color: string }> = {
            '01': { text: '低', color: 'default' },
            '02': { text: '中', color: 'orange' },
            '03': { text: '高', color: 'red' },
            '04': { text: '紧急', color: 'volcano' },
        };
        return priorityMap[priority] || { text: '未知', color: 'default' };
    };

    // if (!appealNo) {
    //     return (
    //         <PageHeaderWrapper>
    //             <ProCard>
    //                 <div style={{ textAlign: 'center', padding: '50px' }}>
    //                     <Text type="secondary">申诉单号不能为空</Text>
    //                 </div>
    //             </ProCard>
    //         </PageHeaderWrapper>
    //     );
    // }

    return (
        <PageHeaderWrapper
            title={
                <div
                    className="page-title"
                    onClick={() => history.goBack()}
                    style={{ cursor: 'pointer' }}
                >
                    <LeftOutlined style={{ marginRight: '8px' }} />
                    申诉管理
                </div>
            }
        >
            <Spin spinning={loading}>
                {appealDetail?.data && (
                    <Row gutter={16}>
                        {/* 左侧主要信息 */}
                        <Col span={16}>
                            {/* 申诉概览 */}
                            <ProCard title="申诉概览" style={{ marginBottom: '16px' }}>
                                <Row gutter={16}>
                                    <Col span={6}>
                                        <Statistic
                                            title="申诉单号"
                                            value={appealDetail.data.appealNo}
                                            valueStyle={{ fontSize: '16px', fontWeight: 'bold' }}
                                        />
                                    </Col>
                                    <Col span={6}>
                                        <Statistic
                                            title="申诉状态"
                                            value={
                                                AppealStatus[
                                                    appealDetail.data
                                                        .appealStatus as keyof typeof AppealStatus
                                                ]
                                            }
                                            valueRender={() => (
                                                <Tag
                                                    color={getStatusTagColor(
                                                        appealDetail.data?.appealStatus || '',
                                                    )}
                                                >
                                                    {
                                                        AppealStatus[
                                                            (appealDetail.data?.appealStatus ||
                                                                '') as keyof typeof AppealStatus
                                                        ]
                                                    }
                                                </Tag>
                                            )}
                                        />
                                    </Col>
                                    <Col span={6}>
                                        <Statistic
                                            title="申诉金额"
                                            value={appealDetail.data.appealAmount || 0}
                                            precision={2}
                                            prefix="¥"
                                            valueStyle={{ color: '#cf1322' }}
                                        />
                                    </Col>
                                    <Col span={6}>
                                        <Statistic
                                            title="优先级"
                                            value={appealDetail.data.priorityName || '普通'}
                                            valueRender={() => {
                                                const priority = getPriorityTag(
                                                    appealDetail.data?.priority || '01',
                                                );
                                                return (
                                                    <Tag color={priority.color}>
                                                        {priority.text}
                                                    </Tag>
                                                );
                                            }}
                                        />
                                    </Col>
                                </Row>

                                {/* 时间信息 */}
                                <Divider />
                                <Row gutter={16}>
                                    <Col span={8}>
                                        <div style={{ textAlign: 'center' }}>
                                            <ClockCircleOutlined
                                                style={{ fontSize: '16px', color: '#1890ff' }}
                                            />
                                            <div style={{ marginTop: '4px' }}>
                                                <Text type="secondary">申诉时间</Text>
                                                <br />
                                                <Text strong>
                                                    {moment(appealDetail.data.appealTime).format(
                                                        'YYYY-MM-DD HH:mm:ss',
                                                    )}
                                                </Text>
                                            </div>
                                        </div>
                                    </Col>
                                    <Col span={8}>
                                        <div style={{ textAlign: 'center' }}>
                                            <ClockCircleOutlined
                                                style={{ fontSize: '16px', color: '#faad14' }}
                                            />
                                            <div style={{ marginTop: '4px' }}>
                                                <Text type="secondary">处理截止时间</Text>
                                                <br />
                                                <Text strong>
                                                    {appealDetail.data.deadlineTime
                                                        ? moment(
                                                              appealDetail.data.deadlineTime,
                                                          ).format('YYYY-MM-DD HH:mm:ss')
                                                        : '无限制'}
                                                </Text>
                                            </div>
                                        </div>
                                    </Col>
                                    <Col span={8}>
                                        <div style={{ textAlign: 'center' }}>
                                            <ClockCircleOutlined
                                                style={{ fontSize: '16px', color: '#52c41a' }}
                                            />
                                            <div style={{ marginTop: '4px' }}>
                                                <Text type="secondary">处理时间</Text>
                                                <br />
                                                <Text strong>
                                                    {appealDetail.data.handleTime
                                                        ? moment(
                                                              appealDetail.data.handleTime,
                                                          ).format('YYYY-MM-DD HH:mm:ss')
                                                        : '未处理'}
                                                </Text>
                                            </div>
                                        </div>
                                    </Col>
                                </Row>
                            </ProCard>

                            {/* 基本信息 */}
                            <ProCard title="基本信息" style={{ marginBottom: '16px' }}>
                                <Descriptions column={2} bordered>
                                    <Descriptions.Item
                                        label={
                                            <>
                                                <UserOutlined /> 用户手机号
                                            </>
                                        }
                                    >
                                        {appealDetail.data.userPhone}
                                    </Descriptions.Item>
                                    <Descriptions.Item
                                        label={
                                            <>
                                                <ShopOutlined /> 商户号
                                            </>
                                        }
                                    >
                                        {appealDetail.data.merchantId}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="申诉类型">
                                        <Tag color="blue">
                                            {
                                                AppealType[
                                                    appealDetail.data
                                                        .appealType as keyof typeof AppealType
                                                ]
                                            }
                                        </Tag>
                                    </Descriptions.Item>
                                    <Descriptions.Item label="关联订单">
                                        {appealDetail.data.orderNo || '-'}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="站点名称">
                                        {appealDetail.data.stationName || '-'}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="运营商">
                                        {appealDetail.data.operatorName || '-'}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="城市">
                                        {appealDetail.data.cityName || '-'}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="处理人">
                                        {appealDetail.data.handlePerson || '-'}
                                    </Descriptions.Item>
                                </Descriptions>
                            </ProCard>

                            {/* 申诉内容 */}
                            <ProCard title="申诉内容" style={{ marginBottom: '16px' }}>
                                <div style={{ marginBottom: '16px' }}>
                                    <Title level={5} style={{ marginBottom: '8px' }}>
                                        申诉标题
                                    </Title>
                                    <Paragraph style={{ fontSize: '16px', marginBottom: '16px' }}>
                                        {appealDetail.data.appealTitle}
                                    </Paragraph>
                                </div>
                                <div style={{ marginBottom: '16px' }}>
                                    <Title level={5} style={{ marginBottom: '8px' }}>
                                        申诉详情
                                    </Title>
                                    <Paragraph
                                        style={{
                                            backgroundColor: '#fafafa',
                                            padding: '12px',
                                            borderRadius: '6px',
                                            border: '1px solid #d9d9d9',
                                        }}
                                    >
                                        {appealDetail.data.appealContent}
                                    </Paragraph>
                                </div>
                                {appealDetail.data.attachments &&
                                    appealDetail.data.attachments.length > 0 && (
                                        <div>
                                            <Title level={5} style={{ marginBottom: '12px' }}>
                                                相关附件
                                            </Title>
                                            <div
                                                style={{
                                                    display: 'flex',
                                                    gap: '12px',
                                                    flexWrap: 'wrap',
                                                }}
                                            >
                                                {appealDetail.data.attachments.map(
                                                    (attachment, index) => (
                                                        <div
                                                            key={index}
                                                            style={{ textAlign: 'center' }}
                                                        >
                                                            <Image
                                                                width={120}
                                                                height={120}
                                                                src={attachment.fileUrl}
                                                                alt={attachment.fileName}
                                                                style={{
                                                                    objectFit: 'cover',
                                                                    borderRadius: '6px',
                                                                    border: '1px solid #d9d9d9',
                                                                }}
                                                            />
                                                            <div
                                                                style={{
                                                                    marginTop: '4px',
                                                                    fontSize: '12px',
                                                                    color: '#666',
                                                                    maxWidth: '120px',
                                                                    overflow: 'hidden',
                                                                    textOverflow: 'ellipsis',
                                                                    whiteSpace: 'nowrap',
                                                                }}
                                                            >
                                                                {attachment.fileName}
                                                            </div>
                                                        </div>
                                                    ),
                                                )}
                                            </div>
                                        </div>
                                    )}
                            </ProCard>

                            {/* 处理结果 */}
                            {appealDetail.data.handleResult && (
                                <ProCard title="处理结果" style={{ marginBottom: '16px' }}>
                                    <Alert
                                        message="处理完成"
                                        description={appealDetail.data.handleRemark}
                                        type="success"
                                        showIcon
                                        style={{ marginBottom: '16px' }}
                                    />
                                    <Descriptions column={2} bordered>
                                        <Descriptions.Item label="处理结果">
                                            <Tag color="green">
                                                {appealDetail.data.handleResult}
                                            </Tag>
                                        </Descriptions.Item>
                                        <Descriptions.Item label="处理人">
                                            {appealDetail.data.handlePerson || '-'}
                                        </Descriptions.Item>
                                        <Descriptions.Item label="处理时间" span={2}>
                                            {appealDetail.data.handleTime
                                                ? moment(appealDetail.data.handleTime).format(
                                                      'YYYY-MM-DD HH:mm:ss',
                                                  )
                                                : '-'}
                                        </Descriptions.Item>
                                        <Descriptions.Item label="处理说明" span={2}>
                                            {appealDetail.data.handleRemark || '-'}
                                        </Descriptions.Item>
                                    </Descriptions>
                                </ProCard>
                            )}
                        </Col>

                        {/* 右侧操作和指引 */}
                        <Col span={8}>
                            {/* 操作区域 */}
                            <ProCard title="操作" style={{ marginBottom: '16px' }}>
                                <AppealActions record={appealDetail.data} onSuccess={refresh} />
                            </ProCard>

                            {/* 操作指引 */}
                            <ProCard title="操作指引" style={{ marginBottom: '16px' }}>
                                <div style={{ lineHeight: '1.8' }}>
                                    <Title level={5} style={{ marginBottom: '12px' }}>
                                        处理流程
                                    </Title>
                                    <div style={{ marginBottom: '16px' }}>
                                        <Text type="secondary">1. 查看申诉详情和相关证据</Text>
                                        <br />
                                        <Text type="secondary">2. 核实申诉内容的真实性</Text>
                                        <br />
                                        <Text type="secondary">3. 根据情况选择处理方式</Text>
                                        <br />
                                        <Text type="secondary">4. 填写处理说明并提交</Text>
                                    </div>

                                    <Title level={5} style={{ marginBottom: '12px' }}>
                                        注意事项
                                    </Title>
                                    <div style={{ marginBottom: '16px' }}>
                                        <Text type="secondary">• 请在截止时间前完成处理</Text>
                                        <br />
                                        <Text type="secondary">• 处理说明需详细说明原因</Text>
                                        <br />
                                        <Text type="secondary">• 如需退款请填写准确金额</Text>
                                        <br />
                                        <Text type="secondary">• 处理结果将通知用户</Text>
                                    </div>

                                    <Title level={5} style={{ marginBottom: '12px' }}>
                                        联系方式
                                    </Title>
                                    <div>
                                        <Text type="secondary">客服热线：400-123-4567</Text>
                                        <br />
                                        <Text type="secondary">工作时间：9:00-18:00</Text>
                                    </div>
                                </div>
                            </ProCard>

                            {/* 处理时间线 */}
                            {appealDetail.data.handleHistory &&
                                appealDetail.data.handleHistory.length > 0 && (
                                    <ProCard title="处理记录">
                                        <Timeline>
                                            {appealDetail.data.handleHistory.map(
                                                (record, index) => (
                                                    <Timeline.Item
                                                        key={index}
                                                        color={index === 0 ? 'green' : 'blue'}
                                                    >
                                                        <div>
                                                            <Text
                                                                strong
                                                                style={{ color: '#1890ff' }}
                                                            >
                                                                {record.operationTypeName}
                                                            </Text>
                                                            <br />
                                                            <Text
                                                                type="secondary"
                                                                style={{ fontSize: '12px' }}
                                                            >
                                                                {moment(
                                                                    record.operationTime,
                                                                ).format('MM-DD HH:mm')}
                                                            </Text>
                                                            <br />
                                                            <Text style={{ fontSize: '13px' }}>
                                                                操作人：{record.operatorName}
                                                            </Text>
                                                            {record.operationContent && (
                                                                <>
                                                                    <br />
                                                                    <Text
                                                                        style={{
                                                                            fontSize: '13px',
                                                                            color: '#666',
                                                                            fontStyle: 'italic',
                                                                        }}
                                                                    >
                                                                        {record.operationContent}
                                                                    </Text>
                                                                </>
                                                            )}
                                                        </div>
                                                    </Timeline.Item>
                                                ),
                                            )}
                                        </Timeline>
                                    </ProCard>
                                )}
                        </Col>
                    </Row>
                )}
            </Spin>
        </PageHeaderWrapper>
    );
};

export default AppealDetailPage;
