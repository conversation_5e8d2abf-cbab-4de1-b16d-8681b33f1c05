export default [
    {
        name: '商家后台管理',
        path: '/merchant',
        title: '商家后台管理',
        icon: 'users',
        routes: [
            {
                path: '/merchant',
                redirect: '/merchant/service-market',
            },
            /*************** 服务市场 ***************/
            {
                path: '/merchant/service-market',
                name: '服务市场',
                title: '服务市场',
                component: '../components/Layouts/FatherLayout',
                routes: [
                    {
                        path: '/merchant/service-market',
                        redirect: '/merchant/service-market/list',
                    },
                    {
                        path: '/merchant/service-market/list',
                        name: '服务市场',
                        title: '服务市场',
                        component: './Merchant/ServiceMarket/List',
                    },
                    {
                        name: '创建服务',
                        title: '创建服务',
                        path: '/merchant/service-market/add',
                        component: './Merchant/ServiceMarket/Add',
                        hideInMenu: true,
                        parentKeys: ['/merchant/service-market/list'],
                    },
                    {
                        name: '编辑服务',
                        title: '编辑服务',
                        path: '/merchant/service-market/edit',
                        component: './Merchant/ServiceMarket/Edit',
                        hideInMenu: true,
                        parentKeys: ['/merchant/service-market/list'],
                    },
                    {
                        name: '服务详情',
                        title: '服务详情',
                        path: '/merchant/service-market/detail',
                        component: './Merchant/ServiceMarket/Detail',
                        hideInMenu: true,
                        parentKeys: ['/merchant/service-market/list'],
                    },
                    {
                        name: '服务配置',
                        title: '服务配置',
                        path: '/merchant/service-market/setting',
                        component: './Merchant/ServiceMarket/Setting',
                        hideInMenu: true,
                        parentKeys: ['/merchant/service-market/list'],
                    },
                    // 电桩商城
                    {
                        path: '/merchant/service-market/pile',
                        name: '电桩商城',
                        title: '电桩商城',
                        component: '../components/Layouts/FatherLayout',
                        routes: [
                            {
                                path: '/merchant/service-market/pile',
                                redirect: '/merchant/service-market/pile/list',
                            },
                            {
                                path: '/merchant/service-market/pile/list',
                                name: '电桩商城',
                                title: '电桩商城',
                                component: './Merchant/PileMarket/List',
                                parentKeys: ['/merchant/service-market'],
                            },
                            {
                                path: '/merchant/service-market/pile/list/add',
                                name: '创建商品',
                                title: '创建商品',
                                component: './Merchant/PileMarket/Add',
                                hideInMenu: true,
                                parentKeys: ['/merchant/service-market/pile/list'],
                            },
                            {
                                path: '/merchant/service-market/pile/list/edit',
                                name: '编辑商品',
                                title: '编辑商品',
                                component: './Merchant/PileMarket/Edit',
                                hideInMenu: true,
                                parentKeys: ['/merchant/service-market/pile/list'],
                            },
                            {
                                path: '/merchant/service-market/pile/list/detail',
                                name: '查看商品',
                                title: '查看商品',
                                component: './Merchant/PileMarket/Detail',
                                hideInMenu: true,
                                parentKeys: ['/merchant/service-market/pile/list'],
                            },
                        ],
                    },
                ],
            },
            {
                path: '/merchant/appealManagement',
                name: '申诉管理',
                title: '申诉管理',
                component: '../components/Layouts/FatherLayout',
                routes: [
                    {
                        path: '/merchant/appealManagement',
                        redirect: '/merchant/appealManagement/list',
                    },
                    {
                        path: '/merchant/appealManagement/list',
                        name: '申诉列表',
                        title: '申诉列表',
                        component: './Merchant/AppealManagement/List',
                    },
                    {
                        path: '/merchant/appealManagement/detail',
                        name: '申诉详情',
                        title: '申诉详情',
                        component: './Merchant/AppealManagement/List',
                        hideInMenu: true,
                        parentKeys: ['/merchant/appealManagement/list'],
                    },
                    {
                        title: '待处理',
                        name: '待处理',
                        path: '/merchant/appealManagement/todo',
                        component: '../components/Layouts/FatherLayout',
                        routes: [
                            {
                                path: '/merchant/appealManagement/todo',
                                redirect: '/merchant/appealManagement/list',
                            },
                            {
                                path: '/merchant/appealManagement/todo/parkingFee',
                                name: '停车费申诉',
                                title: '停车费申诉',
                                component: './Merchant/AppealManagement/List',
                            },
                            {
                                path: '/merchant/appealManagement/todo/spaceOccupancyFee',
                                name: '占位费申诉',
                                title: '占位费申诉',
                                component: './Merchant/AppealManagement/List',
                            },
                        ],
                    },
                    {
                        name: '申诉配置',
                        title: '申诉配置',
                        path: '/merchant/appealManagement/config',
                        component: './Merchant/ServiceMarket/Add',
                    },
                ],
            },
        ],
    },
];
